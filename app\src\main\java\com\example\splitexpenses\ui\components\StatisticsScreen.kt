package com.example.splitexpenses.ui.components

import androidx.compose.animation.AnimatedContent
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.ExperimentalAnimationApi
import androidx.compose.animation.SizeTransform
import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.animateContentSize
import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.animation.expandVertically
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.shrinkVertically
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.animation.with
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.clickable
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.DateRange
import androidx.compose.material.icons.automirrored.filled.KeyboardArrowRight
import androidx.compose.material.icons.automirrored.filled.KeyboardArrowLeft
import androidx.compose.material3.Button
import androidx.compose.material3.DateRangePicker
import androidx.compose.material3.DatePickerDialog
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.rememberDateRangePickerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.runtime.LaunchedEffect
import kotlinx.coroutines.delay
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.example.splitexpenses.R
import com.example.splitexpenses.data.GroupData
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale
import kotlin.toString

enum class PeriodType {
    ALL_TIME,
    THIS_MONTH,
    THIS_YEAR,
    CUSTOM
}

@OptIn(ExperimentalMaterial3Api::class, ExperimentalAnimationApi::class)
@Composable
fun StatisticsScreen(
    group: GroupData?,
    onBackClick: () -> Unit
) {
    if (group == null) return

    var periodType by remember { mutableStateOf(PeriodType.ALL_TIME) }
    var showDatePicker by remember { mutableStateOf(false) }
    var startDate by remember { mutableStateOf<Long?>(null) }
    var endDate by remember { mutableStateOf<Long?>(null) }
    val dateFormat = remember { SimpleDateFormat("dd/MM/yy", Locale.getDefault()) }

    // Add state for member filtering
    var selectedMemberFilter by remember { mutableStateOf<String?>(null) }

    // Current month and year for cycling through periods
    val currentCalendar = Calendar.getInstance()
    var selectedMonth by remember { mutableStateOf(currentCalendar.get(Calendar.MONTH)) }
    var selectedYear by remember { mutableStateOf(currentCalendar.get(Calendar.YEAR)) }

    // Add animation direction state
    var animationDirection by remember { mutableStateOf(0) } // 0: initial, 1: forward, -1: backward

    // Filter expenses based on selected period
    val periodFilteredExpenses = group.expenses.filter { expense ->
        when (periodType) {
            PeriodType.ALL_TIME -> true
            PeriodType.THIS_MONTH -> {
                val expenseCalendar = Calendar.getInstance().apply { timeInMillis = expense.date }
                selectedYear == expenseCalendar.get(Calendar.YEAR) &&
                selectedMonth == expenseCalendar.get(Calendar.MONTH)
            }
            PeriodType.THIS_YEAR -> {
                val expenseCalendar = Calendar.getInstance().apply { timeInMillis = expense.date }
                selectedYear == expenseCalendar.get(Calendar.YEAR)
            }
            PeriodType.CUSTOM -> {
                if (startDate != null && endDate != null) {
                    // Ensure we include the entire start day (from 00:00:00)
                    val startCalendar = Calendar.getInstance().apply {
                        timeInMillis = startDate!!
                        set(Calendar.HOUR_OF_DAY, 0)
                        set(Calendar.MINUTE, 0)
                        set(Calendar.SECOND, 0)
                        set(Calendar.MILLISECOND, 0)
                    }
                    val adjustedStartDate = startCalendar.timeInMillis

                    // Ensure we include the entire end day (until 23:59:59.999)
                    val endCalendar = Calendar.getInstance().apply {
                        timeInMillis = endDate!!
                        set(Calendar.HOUR_OF_DAY, 23)
                        set(Calendar.MINUTE, 59)
                        set(Calendar.SECOND, 59)
                        set(Calendar.MILLISECOND, 999)
                    }
                    val adjustedEndDate = endCalendar.timeInMillis

                    expense.date in adjustedStartDate..adjustedEndDate
                } else {
                    false
                }
            }
        }
    }

    // Apply member filter if one is selected (for charts and calculations)
    val filteredExpenses = if (selectedMemberFilter != null) {
        periodFilteredExpenses.filter { it.paidBy == selectedMemberFilter }
    } else {
        periodFilteredExpenses
    }

    // Helper functions for navigating months and years with animation direction
    fun previousMonth() {
        // Set animation direction to backward
        animationDirection = -1

        if (selectedMonth == 0) {
            selectedMonth = 11
            selectedYear--
        } else {
            selectedMonth--
        }
    }

    fun nextMonth() {
        // Set animation direction to forward
        animationDirection = 1

        if (selectedMonth == 11) {
            selectedMonth = 0
            selectedYear++
        } else {
            selectedMonth++
        }
    }

    fun previousYear() {
        // Set animation direction to backward
        animationDirection = -1
        selectedYear--
    }

    fun nextYear() {
        // Set animation direction to forward
        animationDirection = 1
        selectedYear++
    }

    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // Fixed header
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(onClick = onBackClick) {
                Icon(Icons.AutoMirrored.Filled.ArrowBack, contentDescription = "Back")
            }
            Text(
                text = "Statistics",
                style = MaterialTheme.typography.displayMedium,
                color = MaterialTheme.colorScheme.primary
            )
            // Empty spacer to balance the back button
            Spacer(modifier = Modifier.width(48.dp))
        }

        // Scrollable content
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
                .padding(horizontal = 8.dp)
        ) {
            // Period Selection
            Surface(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 8.dp),
                shape = MaterialTheme.shapes.medium,
                color = MaterialTheme.colorScheme.surface,
                border = BorderStroke(
                    width = 1.dp,
                    color = MaterialTheme.colorScheme.secondaryContainer
                ),
                shadowElevation = 2.dp
            ) {
                Column(
                    modifier = Modifier.padding(8.dp)
                ) {
                    Text(
                        modifier = Modifier
                            .padding(8.dp),
                        text = "Period",
                        style = MaterialTheme.typography.titleMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Spacer(modifier = Modifier.height(2.dp))

                    // Period selector buttons
                    Row(
                        modifier = Modifier
                            .padding(horizontal = 8.dp)
                            .fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(6.dp)
                    ) {
                        // Animate the "All" button colors
                        val allButtonColor = animateColorAsState(
                            targetValue = if(periodType == PeriodType.ALL_TIME)
                                MaterialTheme.colorScheme.primary
                            else
                                MaterialTheme.colorScheme.surfaceVariant,
                            animationSpec = tween(
                                durationMillis = 300,
                                easing = FastOutSlowInEasing
                            ),
                            label = "allButtonColor"
                        )

                        val allTextColor = animateColorAsState(
                            targetValue = if(periodType == PeriodType.ALL_TIME)
                                MaterialTheme.colorScheme.onPrimary
                            else
                                MaterialTheme.colorScheme.onSurfaceVariant,
                            animationSpec = tween(
                                durationMillis = 300,
                                easing = FastOutSlowInEasing
                            ),
                            label = "allTextColor"
                        )

                        Button(
                            onClick = {
                                periodType = PeriodType.ALL_TIME
                                // Reset animation direction when changing period type
                                animationDirection = 0
                            },
                            modifier = Modifier
                                .height(44.dp)
                                .weight(1f),
                            shape = MaterialTheme.shapes.extraLarge,
                            contentPadding = PaddingValues(0.dp),
                            colors = androidx.compose.material3.ButtonDefaults.buttonColors(
                                containerColor = allButtonColor.value,
                                contentColor = allTextColor.value
                            )
                        ) {
                            Text("All", style = MaterialTheme.typography.bodyLarge)
                        }

                        // Animate the "Month" button colors
                        val monthButtonColor = animateColorAsState(
                            targetValue = if(periodType == PeriodType.THIS_MONTH)
                                MaterialTheme.colorScheme.primary
                            else
                                MaterialTheme.colorScheme.surfaceVariant,
                            animationSpec = tween(
                                durationMillis = 300,
                                easing = FastOutSlowInEasing
                            ),
                            label = "monthButtonColor"
                        )

                        val monthTextColor = animateColorAsState(
                            targetValue = if(periodType == PeriodType.THIS_MONTH)
                                MaterialTheme.colorScheme.onPrimary
                            else
                                MaterialTheme.colorScheme.onSurfaceVariant,
                            animationSpec = tween(
                                durationMillis = 300,
                                easing = FastOutSlowInEasing
                            ),
                            label = "monthTextColor"
                        )

                        Button(
                            onClick = {
                                periodType = PeriodType.THIS_MONTH
                                selectedMonth = currentCalendar.get(Calendar.MONTH)
                                selectedYear = currentCalendar.get(Calendar.YEAR)
                                // Reset animation direction when changing period type
                                animationDirection = 0
                            },
                            modifier = Modifier
                                .height(44.dp)
                                .weight(1f),
                            shape = MaterialTheme.shapes.extraLarge,
                            contentPadding = PaddingValues(0.dp),
                            colors = androidx.compose.material3.ButtonDefaults.buttonColors(
                                containerColor = monthButtonColor.value,
                                contentColor = monthTextColor.value
                            )
                        ) {
                            Text("Month", style = MaterialTheme.typography.bodyLarge)
                        }

                        // Animate the "Year" button colors
                        val yearButtonColor = animateColorAsState(
                            targetValue = if(periodType == PeriodType.THIS_YEAR)
                                MaterialTheme.colorScheme.primary
                            else
                                MaterialTheme.colorScheme.surfaceVariant,
                            animationSpec = tween(
                                durationMillis = 300,
                                easing = FastOutSlowInEasing
                            ),
                            label = "yearButtonColor"
                        )

                        val yearTextColor = animateColorAsState(
                            targetValue = if(periodType == PeriodType.THIS_YEAR)
                                MaterialTheme.colorScheme.onPrimary
                            else
                                MaterialTheme.colorScheme.onSurfaceVariant,
                            animationSpec = tween(
                                durationMillis = 300,
                                easing = FastOutSlowInEasing
                            ),
                            label = "yearTextColor"
                        )

                        Button(
                            onClick = {
                                periodType = PeriodType.THIS_YEAR
                                selectedYear = currentCalendar.get(Calendar.YEAR)
                                // Reset animation direction when changing period type
                                animationDirection = 0
                            },
                            modifier = Modifier
                                .height(44.dp)
                                .weight(1f),
                            shape = MaterialTheme.shapes.extraLarge,
                            contentPadding = PaddingValues(0.dp),
                            colors = androidx.compose.material3.ButtonDefaults.buttonColors(
                                containerColor = yearButtonColor.value,
                                contentColor = yearTextColor.value
                            )
                        ) {
                            Text("Year", style = MaterialTheme.typography.bodyLarge)
                        }

                        // Animate the custom date button colors
                        val customButtonColor = animateColorAsState(
                            targetValue = if(periodType == PeriodType.CUSTOM)
                                MaterialTheme.colorScheme.primary
                            else
                                MaterialTheme.colorScheme.surfaceVariant,
                            animationSpec = tween(
                                durationMillis = 300,
                                easing = FastOutSlowInEasing
                            ),
                            label = "customButtonColor"
                        )

                        val customIconTint = animateColorAsState(
                            targetValue = if(periodType == PeriodType.CUSTOM)
                                MaterialTheme.colorScheme.onPrimary
                            else
                                MaterialTheme.colorScheme.onSurfaceVariant,
                            animationSpec = tween(
                                durationMillis = 300,
                                easing = FastOutSlowInEasing
                            ),
                            label = "customIconTint"
                        )

                        Surface(
                            modifier = Modifier
                                .width(44.dp)
                                .height(44.dp)
                                .clickable {
                                    periodType = PeriodType.CUSTOM
                                    showDatePicker = true
                                    // Reset animation direction when changing period type
                                    animationDirection = 0
                                },
                            shape = MaterialTheme.shapes.extraLarge,
                            color = customButtonColor.value
                        ) {
                            Box(
                                modifier = Modifier
                                    .fillMaxSize()
                                    .padding(vertical = 0.dp),
                                contentAlignment = Alignment.Center
                            ) {
                                Icon(
                                    Icons.Default.DateRange,
                                    contentDescription = "Custom Date Range",
                                    tint = customIconTint.value
                                )
                            }
                        }
                    }

                    Spacer(modifier = Modifier.height(8.dp))
                    // Month/Year cycling buttons with animation
                    AnimatedVisibility(
                        visible = periodType == PeriodType.THIS_MONTH || periodType == PeriodType.THIS_YEAR,
                        enter = fadeIn(animationSpec = tween(300)) + expandVertically(animationSpec = tween(300, easing = FastOutSlowInEasing)),
                        exit = fadeOut(animationSpec = tween(300)) + shrinkVertically(animationSpec = tween(300, easing = FastOutSlowInEasing))
                    ) {
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(48.dp)
                                .padding(horizontal = 8.dp),
                            horizontalArrangement = Arrangement.Center,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            // Previous button with animation
                            Surface(
                                modifier = Modifier.size(36.dp),
                                shape = MaterialTheme.shapes.extraLarge,
                                color = MaterialTheme.colorScheme.primaryContainer,
                            ) {
                                IconButton(
                                    onClick = {
                                        if (periodType == PeriodType.THIS_MONTH) previousMonth()
                                        else previousYear()
                                    },
                                    modifier = Modifier.fillMaxSize()
                                ) {
                                    Icon(
                                        Icons.AutoMirrored.Filled.KeyboardArrowLeft,
                                        contentDescription = "Previous",
                                        tint = MaterialTheme.colorScheme.primary
                                    )
                                }
                            }

                            val currentText = when (periodType) {
                                PeriodType.THIS_MONTH -> {
                                    val cal = Calendar.getInstance().apply {
                                        set(Calendar.YEAR, selectedYear)
                                        set(Calendar.MONTH, selectedMonth)
                                    }
                                    val monthName = cal.getDisplayName(Calendar.MONTH, Calendar.LONG, Locale.getDefault())
                                    "$monthName $selectedYear"
                                }
                                PeriodType.THIS_YEAR -> selectedYear.toString()
                                else -> ""
                            }
                            Surface(
                                modifier = Modifier
                                    .padding(horizontal = 4.dp)
                                    .width(140.dp)
                                    .height(36.dp),
                                color = MaterialTheme.colorScheme.primaryContainer,
                                shape = MaterialTheme.shapes.extraLarge
                            ) {
                                Box(
                                    contentAlignment = Alignment.Center
                                ) {
                                    // Animate text changes with explicit direction
                                    AnimatedContent(
                                        targetState = currentText,
                                        transitionSpec = {
                                            // Use explicit animation direction instead of comparing text values
                                            when (animationDirection) {
                                                1 -> { // Forward
                                                    // Going forward (next month/year) - slide up
                                                    slideInVertically { height -> height } + fadeIn() with
                                                    slideOutVertically { height -> -height } + fadeOut()
                                                }
                                                -1 -> { // Backward
                                                    // Going backward (previous month/year) - slide down
                                                    slideInVertically { height -> -height } + fadeIn() with
                                                    slideOutVertically { height -> height } + fadeOut()
                                                }
                                                else -> { // Initial or unknown
                                                    fadeIn() with fadeOut()
                                                }
                                            }.using(
                                                SizeTransform(clip = false)
                                            )
                                        },
                                        label = "periodTextAnimation"
                                    ) { text ->
                                        Box(
                                            modifier = Modifier.fillMaxWidth(),
                                            contentAlignment = Alignment.Center
                                        ) {
                                            Text(
                                                text = text,
                                                style = MaterialTheme.typography.titleMedium,
                                                color = MaterialTheme.colorScheme.onPrimaryContainer,
                                                textAlign = TextAlign.Center
                                            )
                                        }
                                    }
                                }
                            }

                            // Next button with animation
                            Surface(
                                modifier = Modifier.size(36.dp),
                                shape = MaterialTheme.shapes.extraLarge,
                                color = MaterialTheme.colorScheme.primaryContainer,
                            ) {
                                IconButton(
                                    onClick = {
                                        if (periodType == PeriodType.THIS_MONTH) nextMonth()
                                        else nextYear()
                                    },
                                    modifier = Modifier.fillMaxSize()
                                ) {
                                    Icon(
                                        Icons.AutoMirrored.Filled.KeyboardArrowRight,
                                        contentDescription = "Next",
                                        tint = MaterialTheme.colorScheme.primary
                                    )
                                }
                            }
                        }
                    }

                    // Show period information for custom and all-time periods with animation
                    AnimatedVisibility(
                        visible = periodType == PeriodType.ALL_TIME || periodType == PeriodType.CUSTOM,
                        enter = fadeIn(animationSpec = tween(300)) + expandVertically(
                            animationSpec = tween(300, easing = FastOutSlowInEasing)
                        ),
                        exit = fadeOut(animationSpec = tween(300)) + shrinkVertically(
                            animationSpec = tween(300, easing = FastOutSlowInEasing)
                        )
                    ) {
                        val periodText = when (periodType) {
                            PeriodType.ALL_TIME -> {
                                if (filteredExpenses.isNotEmpty()) {
                                    val firstExpenseDate =
                                        Date(filteredExpenses.minOf { it.date })
                                    "Since ${dateFormat.format(firstExpenseDate)}"
                                } else {
                                    "All time"
                                }
                            }

                            PeriodType.CUSTOM -> {
                                if (startDate != null && endDate != null) {
                                    "${dateFormat.format(Date(startDate!!))} - ${
                                        dateFormat.format(
                                            Date(endDate!!)
                                        )
                                    }"
                                } else {
                                    "Custom period"
                                }
                            }

                            else -> ""
                        }

                        // Create a key for the animation based on the period text
                        val textKey = remember(periodText) { periodText.hashCode() }

                        // Animate the text appearance
                        val textAlpha = animateFloatAsState(
                            targetValue = 1f,
                            animationSpec = tween(
                                durationMillis = 300,
                                easing = FastOutSlowInEasing
                            ),
                            label = "periodTextAlpha"
                        )

                        Text(
                            text = periodText,
                            style = MaterialTheme.typography.bodyLarge,
                            color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = textAlpha.value),
                            modifier = Modifier
                                .padding(15.dp)
                                .fillMaxWidth(),
                            textAlign = TextAlign.Center
                        )
                    }
                }
            }

            // Total Expenses Card
            Surface(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 8.dp),
                shape = MaterialTheme.shapes.medium,
                color = MaterialTheme.colorScheme.surface,
                border = BorderStroke(
                    width = 1.dp,
                    color = MaterialTheme.colorScheme.secondaryContainer
                ),
                shadowElevation = 2.dp
            ) {
                Column(
                    modifier = Modifier
                        .animateContentSize()
                        .padding(16.dp)
                ) {
                    Text(
                        text = "Total Expenses",
                        style = MaterialTheme.typography.titleMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    val totalExpenses = filteredExpenses.sumOf { it.amount }
                    // Animate total expenses value
                    val animatedTotalExpenses = animateFloatAsState(
                        targetValue = totalExpenses.toFloat(),
                        animationSpec = tween(
                            durationMillis = 800,
                            easing = FastOutSlowInEasing
                        ),
                        label = "totalExpenses"
                    )
                    Text(
                        text = "${String.format("%.2f", animatedTotalExpenses.value)}€",
                        style = MaterialTheme.typography.headlineMedium,
                        color = MaterialTheme.colorScheme.primary
                    )

                    // Calculate different averages based on period type
                    // Store the average value and unit separately for animation
                    val (averageValue, averageUnit) = when (periodType) {
                        PeriodType.THIS_MONTH -> {
                            // For monthly view: Calculate the daily average by dividing the total expenses
                            // by the actual number of days in the selected month
                            if (filteredExpenses.isEmpty()) {
                                Pair(0.0, "/day")
                            } else {
                                // Get the number of days in the selected month
                                val calendar = Calendar.getInstance()
                                calendar.set(Calendar.YEAR, selectedYear)
                                calendar.set(Calendar.MONTH, selectedMonth)
                                val daysInMonth = calendar.getActualMaximum(Calendar.DAY_OF_MONTH)

                                val avgPerDay = totalExpenses / daysInMonth
                                Pair(avgPerDay, "/day")
                            }
                        }
                        PeriodType.CUSTOM -> {
                            // For custom date range: Calculate the daily average by dividing the total expenses
                            // by the exact number of days in the user-selected period
                            if (filteredExpenses.isEmpty() || startDate == null || endDate == null) {
                                Pair(0.0, "/day")
                            } else {
                                // Calculate the number of days in the custom range (inclusive)
                                val startCalendar = Calendar.getInstance().apply { timeInMillis = startDate!! }
                                val endCalendar = Calendar.getInstance().apply { timeInMillis = endDate!! }

                                // Reset time components to ensure we count full days
                                startCalendar.set(Calendar.HOUR_OF_DAY, 0)
                                startCalendar.set(Calendar.MINUTE, 0)
                                startCalendar.set(Calendar.SECOND, 0)
                                startCalendar.set(Calendar.MILLISECOND, 0)

                                endCalendar.set(Calendar.HOUR_OF_DAY, 0)
                                endCalendar.set(Calendar.MINUTE, 0)
                                endCalendar.set(Calendar.SECOND, 0)
                                endCalendar.set(Calendar.MILLISECOND, 0)

                                // Calculate days between (inclusive, so add 1)
                                val diffInMillis = endCalendar.timeInMillis - startCalendar.timeInMillis
                                val days = maxOf(1, (diffInMillis / (24 * 60 * 60 * 1000)) + 1)

                                val avgPerDay = totalExpenses / days
                                Pair(avgPerDay, "/day")
                            }
                        }
                        PeriodType.THIS_YEAR -> {
                            // For year: average per month
                            if (filteredExpenses.isEmpty()) {
                                Pair(0.0, "/month")
                            } else {
                                val avgPerMonth = totalExpenses / 12
                                Pair(avgPerMonth, "/month")
                            }
                        }
                        PeriodType.ALL_TIME -> {
                            // For all-time view: Calculate the monthly average by dividing the total expenses
                            // by the number of months between the date of the first recorded expense and today's date
                            if (filteredExpenses.isEmpty()) {
                                Pair(0.0, "/month")
                            } else {
                                // Get the date of the first recorded expense
                                val firstExpenseDate = filteredExpenses.minOf { it.date }

                                // Get today's date
                                val today = Calendar.getInstance().timeInMillis

                                // Calculate the number of months between the first expense and today
                                val firstExpenseCal = Calendar.getInstance().apply { timeInMillis = firstExpenseDate }
                                val todayCal = Calendar.getInstance().apply { timeInMillis = today }

                                // Calculate months between (including partial months)
                                val yearDiff = todayCal.get(Calendar.YEAR) - firstExpenseCal.get(Calendar.YEAR)
                                val monthDiff = todayCal.get(Calendar.MONTH) - firstExpenseCal.get(Calendar.MONTH)
                                val dayDiff = todayCal.get(Calendar.DAY_OF_MONTH) - firstExpenseCal.get(Calendar.DAY_OF_MONTH)

                                // Calculate total months with decimal for partial months
                                var totalMonths = (yearDiff * 12 + monthDiff).toDouble()

                                // Add partial month if there's a day difference
                                if (dayDiff != 0) {
                                    val daysInMonth = firstExpenseCal.getActualMaximum(Calendar.DAY_OF_MONTH)
                                    val partialMonth = dayDiff.toDouble() / daysInMonth.toDouble()
                                    if (dayDiff > 0) {
                                        totalMonths = totalMonths + partialMonth
                                    } else {
                                        totalMonths = totalMonths + 1.0 + partialMonth // partial month is negative, so add 1 and then the negative fraction
                                    }
                                }

                                // Ensure we have at least 0.1 months to avoid division by zero or tiny denominators
                                val months = maxOf(0.1, totalMonths)

                                val avgPerMonth = totalExpenses / months
                                Pair(avgPerMonth, "/month")
                            }
                        }
                    }

                    // Create a unique key for the current average to trigger animations
                    val averageKey = remember(periodType, filteredExpenses.size, totalExpenses) {
                        "${periodType}_${filteredExpenses.size}_${totalExpenses}"
                    }

                    // Animate the average value
                    val animatedAverageValue = animateFloatAsState(
                        targetValue = averageValue.toFloat(),
                        animationSpec = tween(
                            durationMillis = 800,
                            easing = FastOutSlowInEasing
                        ),
                        label = "averageValue"
                    )

                    // Display the animated average
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "Average: ",
                            style = MaterialTheme.typography.bodyLarge,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )

                        // Animated value with unit
                        Text(
                            text = "${String.format("%.2f", animatedAverageValue.value)}€${averageUnit}",
                            style = MaterialTheme.typography.bodyLarge,
                            color = MaterialTheme.colorScheme.primary
                        )
                    }

                    // Animate the expense count
                    val animatedExpenseCount = animateFloatAsState(
                        targetValue = filteredExpenses.size.toFloat(),
                        animationSpec = tween(
                            durationMillis = 800,
                            easing = FastOutSlowInEasing
                        ),
                        label = "expenseCount"
                    )

                    // Show number of expenses with animation
                    Text(
                        text = "${animatedExpenseCount.value.toInt()} expense${if (animatedExpenseCount.value.toInt() != 1) "s" else ""}",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }

            Spacer(modifier = Modifier.height(8.dp))

            // Expenses by Category Card
            Surface(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 8.dp),
                    //.animateContentSize(),
                shape = MaterialTheme.shapes.medium,
                color = MaterialTheme.colorScheme.surface,
                border = BorderStroke(
                    width = 1.dp,
                    color = MaterialTheme.colorScheme.secondaryContainer
                ),
                shadowElevation = 2.dp
            ) {
                Column(
                    modifier = Modifier
                        .padding(16.dp)
                        .animateContentSize(),
                ) {
                    Text(
                        text = "Expenses by Category",
                        style = MaterialTheme.typography.titleMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    // Create a complete map of all categories with their amounts (including 0)
                    val expenseAmountsByCategory = filteredExpenses.groupBy { it.category }
                        .mapValues { it.value.sumOf { expense -> expense.amount } }

                    // Include all group categories, even those with 0 expenses, for better animations
                    val allCategoriesWithAmounts = group.categories.map { category ->
                        category.name to (expenseAmountsByCategory[category.name] ?: 0.0)
                    }.toMap()

                    // Add any custom categories that aren't in the group's categories list
                    val customCategories = expenseAmountsByCategory.keys.filter { categoryName ->
                        group.categories.none { it.name == categoryName }
                    }
                    val customCategoriesWithAmounts = customCategories.associateWith { categoryName ->
                        expenseAmountsByCategory[categoryName] ?: 0.0
                    }

                    // Combine all categories and sort by group order
                    val expensesByCategory = (allCategoriesWithAmounts + customCategoriesWithAmounts)
                        .toList()
                        .sortedBy { (categoryName, _) ->
                            // Sort by the order of categories in the group's categories list
                            // Categories not in the list (custom ones) will appear at the end
                            val index = group.categories.indexOfFirst { it.name == categoryName }
                            if (index == -1) Int.MAX_VALUE else index
                        }

                    // For the legend, only show categories with expenses > 0
                    val expensesByCategoryForLegend = expensesByCategory.filter { it.second > 0 }

                    // Add Pie Chart
                    if (expensesByCategory.isNotEmpty()) {
                        // Calculate total amount for percentages and thresholds (only from categories with expenses)
                        val totalAmount = expensesByCategoryForLegend.sumOf { it.second }
                        // Generate dynamic colors for the pie chart based on the number of categories
                        val baseColors = listOf(
                            Color(255, 100, 100), // Soft Red
                            Color(255, 160, 100), // Warm Orange
                            Color(255, 200, 130), // Golden Yellow
                            Color(130, 200, 130), // Soft Green
                            Color(130, 200, 255), // Sky Blue
                            Color(130, 160, 255), // Soft Blue
                            Color(130, 130, 255), // Blue
                            Color(170, 130, 255), // Violet Purple
                            Color(255, 130, 200), // Pink Magenta
                        )

                        // Generate additional colors if needed by interpolating between existing colors
                        val colors = if (expensesByCategory.size <= baseColors.size) {
                            baseColors
                        } else {
                            val dynamicColors = mutableListOf<Color>()
                            dynamicColors.addAll(baseColors)

                            // Generate additional colors by varying the hue
                            for (i in baseColors.size until expensesByCategory.size) {
                                val baseColor = baseColors[i % baseColors.size]

                                // Extract RGB and move them closer to 255 (white)
                                val r = ((baseColor.red * 255) + (255 * 0.2f)).toInt().coerceIn(0, 255)
                                val g = ((baseColor.green * 255) + (255 * 0.2f)).toInt().coerceIn(0, 255)
                                val b = ((baseColor.blue * 255) + (255 * 0.2f)).toInt().coerceIn(0, 255)

                                dynamicColors.add(Color(r, g, b))
                            }
                            dynamicColors
                        }

                        // Show pie chart
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(300.dp)
                                .padding(8.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            // Determine if we need to group small slices for the pie chart
                            val minSlicePercentage = 2.0 // Minimum percentage for a slice to be shown individually
                            // Add safety check to prevent multiplication with zero
                            val smallSliceThreshold = if (totalAmount > 0) {
                                totalAmount * (minSlicePercentage / 100.0)
                            } else {
                                0.01 // Small non-zero value as fallback
                            }

                            // Group small slices if there are too many categories (use legend data for this logic)
                            val (significantCategories, smallCategories) = if (expensesByCategoryForLegend.size > 15) {
                                expensesByCategoryForLegend.partition { (_, amount) -> amount >= smallSliceThreshold }
                            } else {
                                Pair(expensesByCategoryForLegend, emptyList())
                            }

                            // Calculate the sum of small slices
                            val smallSlicesSum = smallCategories.sumOf { it.second }

                            // Prepare final data for the pie chart - use ALL categories (including zeros) for better animations
                            val finalPieChartData = expensesByCategory.map { it.second }

                            // Prepare colors for the pie chart using original category indices - use ALL categories
                            val pieChartColors = expensesByCategory.map { (categoryName, _) ->
                                val originalIndex = group.categories.indexOfFirst { it.name == categoryName }
                                if (originalIndex != -1) colors[originalIndex % colors.size] else colors[0]
                            }

                            PieChart(
                                data = finalPieChartData,
                                colors = pieChartColors
                            )

                            // Add total amount text overlay
                            Column(
                                horizontalAlignment = Alignment.CenterHorizontally,
                                verticalArrangement = Arrangement.Center
                            ) {
                                Text(
                                    text = "Total",
                                    style = MaterialTheme.typography.bodySmall,
                                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                                )
                                // Animate total amount
                                val animatedTotalAmount = animateFloatAsState(
                                    targetValue = totalAmount.toFloat(),
                                    animationSpec = tween(
                                        durationMillis = 800,
                                        easing = FastOutSlowInEasing
                                    ),
                                    label = "totalAmount"
                                )
                                Text(
                                    text = "${String.format("%.0f", animatedTotalAmount.value)}€",
                                    style = MaterialTheme.typography.titleLarge,
                                    color = MaterialTheme.colorScheme.onSurface
                                )
                            }
                        }

                        // Add legend with scrollable container for many categories
                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(horizontal = 16.dp, vertical = 8.dp)
                                .animateContentSize()
                        ) {
                            // Show legend title with category count (only categories with expenses)
                            Text(
                                text = "${expensesByCategoryForLegend.size} Categories",
                                style = MaterialTheme.typography.titleSmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant,
                                modifier = Modifier.padding(bottom = 8.dp)
                            )

                            // Use the same logic for legend as we did for the pie chart
                            val minSlicePercentage = 2.0 // Minimum percentage for a slice to be shown individually
                            // Add safety check to prevent multiplication with zero
                            val smallSliceThreshold = if (totalAmount > 0) {
                                totalAmount * (minSlicePercentage / 100.0)
                            } else {
                                0.01 // Small non-zero value as fallback
                            }

                            // Group small slices if there are too many categories (use legend data)
                            val (significantCategoriesForLegend, smallCategoriesForLegend) = if (expensesByCategoryForLegend.size > 15) {
                                expensesByCategoryForLegend.partition { (_, amount) -> amount >= smallSliceThreshold }
                            } else {
                                Pair(expensesByCategoryForLegend, emptyList())
                            }

                            // Calculate the sum of small slices for legend
                            val smallSlicesSumForLegend = smallCategoriesForLegend.sumOf { it.second }

                            // Prepare final categories for the legend
                            val finalCategories = significantCategoriesForLegend.toMutableList()
                            if (smallSlicesSumForLegend > 0) {
                                finalCategories.add(Pair("Other (${smallCategoriesForLegend.size} categories)", smallSlicesSumForLegend))
                            }

                            // Create scrollable container for legend items
                            val legendHeight = if (finalCategories.size > 5) 200.dp else (finalCategories.size * 40).dp
                            Column(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .height(legendHeight)
                                    .verticalScroll(rememberScrollState())
                            ) {
                                finalCategories.forEach { item ->
                                    val category = item.first
                                    val amount = item.second
                                    // Use the original category index from group.categories for consistent colors
                                    val originalCategoryIndex = group.categories.indexOfFirst { it.name == category }
                                    val colorIndex = if (originalCategoryIndex != -1) originalCategoryIndex else finalCategories.indexOf(item)
                                    // Add safety check to prevent division by zero
                                    val percentage = if (totalAmount > 0) {
                                        (amount / totalAmount * 100).toInt()
                                    } else {
                                        0
                                    }
                                    val isOtherCategory = category.startsWith("Other")

                                    Row(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .padding(vertical = 4.dp),
                                        horizontalArrangement = Arrangement.SpaceBetween,
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Row(
                                            verticalAlignment = Alignment.CenterVertically,
                                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                                            modifier = Modifier.weight(1f)
                                        ) {
                                            // Color indicator with animation
                                            val animatedColor = animateColorAsState(
                                                targetValue = if (isOtherCategory) Color.Gray else colors[colorIndex % colors.size],
                                                animationSpec = tween(durationMillis = 500),
                                                label = "legendColor"
                                            )
                                            Box(
                                                modifier = Modifier
                                                    .size(12.dp)
                                                    .background(
                                                        animatedColor.value,
                                                        shape = MaterialTheme.shapes.small
                                                    )
                                            )

                                            // Emoji
                                            Text(
                                                text = if (isOtherCategory) "🔄" else (group.categories.find { it.name == category }?.emoji ?: "💰"),
                                                style = MaterialTheme.typography.titleMedium
                                            )

                                            // Category name with ellipsis if too long
                                            Text(
                                                text = category,
                                                style = MaterialTheme.typography.bodyLarge,
                                                maxLines = 1,
                                                overflow = TextOverflow.Ellipsis,
                                                modifier = Modifier.weight(1f, fill = false)
                                            )
                                        }

                                        Row(
                                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                                        ) {
                                            // Animate percentage value
                                            val animatedPercentage = animateFloatAsState(
                                                targetValue = percentage.toFloat(),
                                                animationSpec = tween(durationMillis = 500),
                                                label = "percentageValue"
                                            )
                                            Text(
                                                text = "${animatedPercentage.value.toInt()}%",
                                                style = MaterialTheme.typography.bodyMedium,
                                                color = MaterialTheme.colorScheme.onSurfaceVariant
                                            )

                                            // Animate amount value
                                            val animatedAmount = animateFloatAsState(
                                                targetValue = amount.toFloat(),
                                                animationSpec = tween(durationMillis = 500),
                                                label = "amountValue"
                                            )
                                            Text(
                                                text = "${String.format("%.2f", animatedAmount.value)}€",
                                                style = MaterialTheme.typography.bodyLarge,
                                                color = MaterialTheme.colorScheme.primary
                                            )
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            Spacer(modifier = Modifier.height(8.dp))

            // Expenses by Person Card
            Surface(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 8.dp)
                    .animateContentSize(),
                shape = MaterialTheme.shapes.medium,
                color = MaterialTheme.colorScheme.surface,
                border = BorderStroke(
                    width = 1.dp,
                    color = MaterialTheme.colorScheme.secondaryContainer
                ),
                shadowElevation = 2.dp
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "Expenses by Person",
                            style = MaterialTheme.typography.titleMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )

                        // Add a "Clear filter" button if a member filter is active
//                        if (selectedMemberFilter != null) {
//                            TextButton(
//                                onClick = { selectedMemberFilter = null },
////                                contentPadding = PaddingValues(horizontal = 8.dp, vertical = 0.dp)
//                            ) {
//                                Text(
//                                    text = "Clear filter",
//                                    style = MaterialTheme.typography.bodyMedium,
//                                    color = MaterialTheme.colorScheme.primary
//                                )
//                            }
//                        }
                    }

//                    // Show active filter information if a member is selected
//                    if (selectedMemberFilter != null) {
//                        Row(
//                            modifier = Modifier
//                                .fillMaxWidth()
//                                .padding(vertical = 4.dp),
//                            verticalAlignment = Alignment.CenterVertically
//                        ) {
//                            Text(
//                                text = "Showing expenses paid by: ",
//                                style = MaterialTheme.typography.bodyMedium,
//                                color = MaterialTheme.colorScheme.onSurfaceVariant
//                            )
//                            Text(
//                                text = selectedMemberFilter!!,
//                                style = MaterialTheme.typography.bodyMedium,
//                                color = MaterialTheme.colorScheme.primary,
//                                fontWeight = androidx.compose.ui.text.font.FontWeight.Bold
//                            )
//                        }
//                    }

//                    // Show active filter information if a member is selected
//                    if (selectedMemberFilter != null) {
//                        Surface(
//                            modifier = Modifier
//                                .fillMaxWidth()
//                                .padding(vertical = 8.dp),
//                            color = MaterialTheme.colorScheme.primaryContainer,
//                            shape = MaterialTheme.shapes.small
//                        ) {
//                            Row(
//                                modifier = Modifier
//                                    .fillMaxWidth()
//                                    .padding(8.dp),
//                                verticalAlignment = Alignment.CenterVertically,
//                                horizontalArrangement = Arrangement.Center
//                            ) {
//                                Text(
//                                    text = "Filtered by: ",
//                                    style = MaterialTheme.typography.bodyMedium,
//                                    color = MaterialTheme.colorScheme.onPrimaryContainer
//                                )
//                                Text(
//                                    text = selectedMemberFilter!!,
//                                    style = MaterialTheme.typography.bodyMedium,
//                                    fontWeight = androidx.compose.ui.text.font.FontWeight.Bold,
//                                    color = MaterialTheme.colorScheme.onPrimaryContainer
//                                )
//                            }
//                        }
//                    }

                    Spacer(modifier = Modifier.height(8.dp))

                    // Always use periodFilteredExpenses to show all members regardless of filter
                    val expensesByPerson = periodFilteredExpenses.groupBy { it.paidBy }
                        .mapValues { it.value.sumOf { expense -> expense.amount } }
                        .toList()
                        .sortedByDescending { it.second }

                    // Calculate total for percentage with safety check
                    val totalByAllPersons = expensesByPerson.sumOf { it.second }

                    expensesByPerson.forEach { (person, amount) ->
                        val isSelected = selectedMemberFilter == person
                        // Add safety check to prevent division by zero
                        val percentage = if (totalByAllPersons > 0) {
                            (amount / totalByAllPersons * 100).toInt()
                        } else {
                            0
                        }

                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 4.dp)
                                .clickable {
                                    // Toggle selection: if already selected, clear the filter, otherwise set it
                                    selectedMemberFilter = if (isSelected) null else person
                                }
                                // Animate background color for the selected member
                                .background(
                                    color = animateColorAsState(
                                        targetValue = if (isSelected)
                                            MaterialTheme.colorScheme.primaryContainer
                                        else
                                            Color.Transparent,
                                        animationSpec = tween(durationMillis = 300),
                                        label = "memberBackgroundColor"
                                    ).value,
                                    shape = MaterialTheme.shapes.small
                                )
                                .padding(8.dp),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.spacedBy(8.dp),
                                modifier = Modifier.weight(1f)
                            ) {
                                // Add a filter indicator for the selected member
//                                if (isSelected) {
//                                    Icon(
//                                        Icons.Default.DateRange, // Using DateRange as a filter icon
//                                        contentDescription = "Filter active",
//                                        modifier = Modifier.size(16.dp),
//                                        tint = MaterialTheme.colorScheme.onPrimaryContainer
//                                    )
//                                }

                                // Animate icon tint
                                val animatedIconTint = animateColorAsState(
                                    targetValue = if (isSelected)
                                        MaterialTheme.colorScheme.onPrimaryContainer
                                    else
                                        MaterialTheme.colorScheme.onSurfaceVariant,
                                    animationSpec = tween(durationMillis = 300),
                                    label = "iconTint"
                                )
                                Icon(
                                    painter = painterResource(id = R.drawable.account_outline),
                                    contentDescription = null,
                                    modifier = Modifier.size(20.dp),
                                    tint = animatedIconTint.value
                                )
                                // Animate text color for the selected member
                                val animatedTextColor = animateColorAsState(
                                    targetValue = if (isSelected)
                                        MaterialTheme.colorScheme.onPrimaryContainer
                                    else
                                        MaterialTheme.colorScheme.onSurface,
                                    animationSpec = tween(durationMillis = 300),
                                    label = "memberTextColor"
                                )
                                Text(
                                    text = person,
                                    style = MaterialTheme.typography.bodyLarge,
                                    color = animatedTextColor.value,
                                    fontWeight = if (isSelected)
                                        androidx.compose.ui.text.font.FontWeight.Bold
                                    else
                                        androidx.compose.ui.text.font.FontWeight.Normal
                                )
                            }

                            Row(
                                horizontalArrangement = Arrangement.spacedBy(8.dp)
                            ) {
                                // Animate percentage value and color
                                val animatedPercentageValue = animateFloatAsState(
                                    targetValue = percentage.toFloat(),
                                    animationSpec = tween(durationMillis = 500),
                                    label = "personPercentage"
                                )
                                val animatedPercentageColor = animateColorAsState(
                                    targetValue = if (isSelected)
                                        MaterialTheme.colorScheme.onPrimaryContainer
                                    else
                                        MaterialTheme.colorScheme.onSurfaceVariant,
                                    animationSpec = tween(durationMillis = 300),
                                    label = "percentageColor"
                                )
                                Text(
                                    text = "${animatedPercentageValue.value.toInt()}%",
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = animatedPercentageColor.value
                                )

                                // Animate amount value
                                val animatedAmountValue = animateFloatAsState(
                                    targetValue = amount.toFloat(),
                                    animationSpec = tween(durationMillis = 500),
                                    label = "personAmount"
                                )
                                Text(
                                    text = "${String.format("%.2f", animatedAmountValue.value)}€",
                                    style = MaterialTheme.typography.bodyLarge,
                                    color = MaterialTheme.colorScheme.primary,
                                    fontWeight = if (isSelected)
                                        androidx.compose.ui.text.font.FontWeight.Bold
                                    else
                                        androidx.compose.ui.text.font.FontWeight.Normal
                                )
                            }
                        }
                    }
                }
            }
           // item {
            Spacer(modifier = Modifier.height(80.dp))
            //}
        }
    }

    // Date picker dialog for custom period
    if (showDatePicker) {
        val dateRangePickerState = rememberDateRangePickerState()
        DatePickerDialog(
            onDismissRequest = {
                showDatePicker = false
                if (startDate == null) {
                    periodType = PeriodType.ALL_TIME
                }
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        dateRangePickerState.selectedStartDateMillis?.let { start ->
                            dateRangePickerState.selectedEndDateMillis?.let { end ->

                                startDate = start
                                endDate = end
                                showDatePicker = false
                            }
                        }
                    }
                ) {
                    Text("OK")
                }
            },
            dismissButton = {
                TextButton(
                    onClick = {
                        showDatePicker = false
                        if (startDate == null) {
                            periodType = PeriodType.ALL_TIME
                        }
                    }
                ) {
                    Text("Cancel")
                }
            }
        ) {
            DateRangePicker(
                state = dateRangePickerState,
                title = {
                    Text(
                        modifier = Modifier
                            .padding(16.dp),
                        text = "Select date range"
                    )
                },
                showModeToggle = false,
                modifier = Modifier
                    .fillMaxWidth()
                    .height(500.dp)
                    .padding(2.dp)
            )
        }
    }
}

/**
 * A basic pie chart component without animations.
 * This provides a clean starting point for implementing custom animations.
 */
@Composable
fun PieChart(
    data: List<Double>,
    colors: List<Color>
) {

    // State for tracking animation and data
    val isFirstAppearance = remember { mutableStateOf(true) }

    // Use two separate states to store current and target data
    // This allows us to animate between them without flickering
    val currentData = remember { mutableStateOf(emptyList<Double>()) }
    val currentColors = remember { mutableStateOf(emptyList<Color>()) }
    val currentTotal = remember { mutableStateOf(0.0) }

    val targetData = remember { mutableStateOf(emptyList<Double>()) }
    val targetColors = remember { mutableStateOf(emptyList<Color>()) }
    val targetTotal = remember { mutableStateOf(0.0) }

    // Animation progress from 0 (showing current) to 1 (showing target)
    val animationProgress = remember { Animatable(0f) }

    // Calculate total for percentage calculations
    val total = data.sum()

    // Create a unique identifier for the current data set
    val dataFingerprint = data.joinToString { it.toString() }

    // Handle data changes and animation
    LaunchedEffect(dataFingerprint) {
        // Update target data with the new values
        targetData.value = data
        targetColors.value = colors
        targetTotal.value = total

        if (isFirstAppearance.value) {
            // First appearance - also set current data to match target
            // This prevents animation from empty state
            currentData.value = data
            currentColors.value = colors
            currentTotal.value = total

            // No need to animate for first appearance
            animationProgress.snapTo(1f)
            isFirstAppearance.value = false
        } else {
            // Reset animation to 0 (showing current data)
            animationProgress.snapTo(0f)

            // Animate to 1 (showing target data)
            animationProgress.animateTo(
                targetValue = 1f,
                animationSpec = tween(
                    durationMillis = 800,
                    easing = FastOutSlowInEasing
                )
            )

            // After animation completes, update current data to match target
            currentData.value = targetData.value
            currentColors.value = targetColors.value
            currentTotal.value = targetTotal.value
        }
    }

    if (data.isEmpty()) return

    // Fixed start angle at 12 o'clock position
    val startAngle = -90f

    Canvas(
        modifier = Modifier
            .aspectRatio(1f)
            .padding(8.dp)
    ) {
        val progress = animationProgress.value

        val canvasSize = size.minDimension
        val radius = canvasSize / 2
        val center = Offset(size.width / 2, size.height / 2)
        val strokeColor = Color.Black.copy(alpha = 0.5f)

        var currentStartAngle = startAngle

        // Calculate angle per value for both current and target data
        val targetAnglePerValue = if (targetTotal.value > 0) {
            360f / targetTotal.value.toFloat()
        } else {
            0f
        }

        val currentAnglePerValue = if (currentTotal.value > 0) {
            360f / currentTotal.value.toFloat()
        } else {
            0f
        }

        // Determine which data set has more elements
        val maxDataSize = maxOf(currentData.value.size, targetData.value.size)

        // Draw each sector with interpolation between current and target values
        for (i in 0 until maxDataSize) {
            // Get current and target values safely
            val currentValue = currentData.value.getOrNull(i) ?: 0.0
            val targetValue = targetData.value.getOrNull(i) ?: 0.0

            // Interpolate between current and target values based on animation progress
            val interpolatedValue = currentValue * (1 - progress) + targetValue * progress

            // Calculate sweep angle with interpolation
            val currentSweepAngle = currentValue.toFloat() * currentAnglePerValue
            val targetSweepAngle = targetValue.toFloat() * targetAnglePerValue
            val sweepAngle = currentSweepAngle * (1 - progress) + targetSweepAngle * progress

            // Only draw if the sweep angle is visible
            if (sweepAngle >= 0.5f) {
                // Get color with interpolation if possible
                val color = if (i < currentColors.value.size && i < targetColors.value.size) {
                    // Both colors available - could interpolate between them
                    // For simplicity, we'll just use the target color
                    targetColors.value[i]
                } else if (i < targetColors.value.size) {
                    // Only target color available
                    targetColors.value[i]
                } else if (i < currentColors.value.size) {
                    // Only current color available
                    currentColors.value[i]
                } else {
                    // No color available - use a default
                    Color.Gray
                }

                // Draw the sector
                drawArc(
                    color = color,
                    startAngle = currentStartAngle,
                    sweepAngle = sweepAngle,
                    useCenter = true,
                    topLeft = Offset(center.x - radius, center.y - radius),
                    size = Size(radius * 2, radius * 2)
                )

                // Draw stroke
                drawArc(
                    color = strokeColor,
                    startAngle = currentStartAngle,
                    sweepAngle = sweepAngle,
                    useCenter = true,
                    style = Stroke(width = 1.5f),
                    topLeft = Offset(center.x - radius, center.y - radius),
                    size = Size(radius * 2, radius * 2)
                )
            }

            // Update start angle for next sector
            currentStartAngle += sweepAngle
        }

        // Draw center circle for donut chart effect
        val centerColor = Color.Black
        drawCircle(
            color = centerColor,
            radius = radius * 0.6f,
            center = center
        )
    }
}
